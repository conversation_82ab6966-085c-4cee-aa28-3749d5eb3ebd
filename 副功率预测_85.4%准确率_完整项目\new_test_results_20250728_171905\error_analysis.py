#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
误差分析脚本 - 生成详细的误差统计和分析
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path

def analyze_prediction_errors():
    """分析预测误差"""
    print("="*80)
    print("📊 副功率预测误差详细分析")
    print("="*80)
    
    # 读取所有测试结果
    test_files = [
        ("时间序列分割测试", "时间序列分割测试_predictions.csv"),
        ("随机分割测试", "随机分割测试_predictions.csv"),
        ("设备分割测试", "设备分割测试_predictions.csv")
    ]
    
    all_results = {}
    total_samples = 0
    total_within_10 = 0
    
    for test_name, filename in test_files:
        if Path(filename).exists():
            df = pd.read_csv(filename)
            all_results[test_name] = df
            total_samples += len(df)
            total_within_10 += df['within_10kWh'].sum()
            
            print(f"\n{test_name}:")
            print(f"  样本数: {len(df)}")
            print(f"  ±10kWh内样本: {df['within_10kWh'].sum()}/{len(df)} ({df['within_10kWh'].mean()*100:.2f}%)")
            
            # 详细误差统计
            errors = df['absolute_error']
            print(f"  平均绝对误差: {errors.mean():.2f} kWh")
            print(f"  误差标准差: {errors.std():.2f} kWh")
            print(f"  误差中位数: {errors.median():.2f} kWh")
            print(f"  90%分位数: {errors.quantile(0.9):.2f} kWh")
            print(f"  95%分位数: {errors.quantile(0.95):.2f} kWh")
            print(f"  99%分位数: {errors.quantile(0.99):.2f} kWh")
    
    print(f"\n{'='*80}")
    print(f"📈 总体统计")
    print(f"{'='*80}")
    print(f"总测试样本数: {total_samples}")
    print(f"总体±10kWh准确率: {total_within_10}/{total_samples} ({total_within_10/total_samples*100:.2f}%)")
    
    return all_results

def detailed_error_breakdown():
    """详细的误差分解分析"""
    print(f"\n{'='*80}")
    print(f"🔍 详细误差分解分析")
    print(f"{'='*80}")
    
    test_files = [
        ("时间序列分割测试", "时间序列分割测试_predictions.csv"),
        ("随机分割测试", "随机分割测试_predictions.csv"),
        ("设备分割测试", "设备分割测试_predictions.csv")
    ]
    
    # 误差区间定义
    error_ranges = [
        (0, 1, "0-1kWh"),
        (1, 2, "1-2kWh"),
        (2, 5, "2-5kWh"),
        (5, 10, "5-10kWh"),
        (10, 15, "10-15kWh"),
        (15, 20, "15-20kWh"),
        (20, 30, "20-30kWh"),
        (30, 50, "30-50kWh"),
        (50, 100, "50-100kWh"),
        (100, float('inf'), ">100kWh")
    ]
    
    for test_name, filename in test_files:
        if Path(filename).exists():
            df = pd.read_csv(filename)
            errors = df['absolute_error']
            
            print(f"\n{test_name} - 详细误差分布:")
            print(f"{'误差范围':<12} {'样本数':<8} {'百分比':<8} {'累计百分比':<10}")
            print("-" * 45)
            
            cumulative_count = 0
            for low, high, label in error_ranges:
                if high == float('inf'):
                    count = (errors > low).sum()
                else:
                    count = ((errors > low) & (errors <= high)).sum()
                
                cumulative_count += count
                percentage = count / len(errors) * 100
                cumulative_percentage = cumulative_count / len(errors) * 100
                
                print(f"{label:<12} {count:<8} {percentage:<7.1f}% {cumulative_percentage:<9.1f}%")
    
    return True

def analyze_prediction_quality():
    """分析预测质量"""
    print(f"\n{'='*80}")
    print(f"🎯 预测质量分析")
    print(f"{'='*80}")
    
    test_files = [
        ("时间序列分割测试", "时间序列分割测试_predictions.csv"),
        ("随机分割测试", "随机分割测试_predictions.csv"),
        ("设备分割测试", "设备分割测试_predictions.csv")
    ]
    
    for test_name, filename in test_files:
        if Path(filename).exists():
            df = pd.read_csv(filename)
            
            print(f"\n{test_name}:")
            
            # 预测范围分析
            actual_min, actual_max = df['actual_vice_power'].min(), df['actual_vice_power'].max()
            pred_min, pred_max = df['predicted_vice_power'].min(), df['predicted_vice_power'].max()
            
            print(f"  实际值范围: {actual_min:.1f} - {actual_max:.1f} kWh")
            print(f"  预测值范围: {pred_min:.1f} - {pred_max:.1f} kWh")
            print(f"  范围覆盖率: {(pred_max - pred_min) / (actual_max - actual_min) * 100:.1f}%")
            
            # 相关性分析
            correlation = df['actual_vice_power'].corr(df['predicted_vice_power'])
            print(f"  预测-实际相关性: {correlation:.4f}")
            
            # 偏差分析
            bias = (df['predicted_vice_power'] - df['actual_vice_power']).mean()
            print(f"  平均偏差: {bias:.2f} kWh")
            
            # 不同功率范围的准确率
            power_ranges = [
                (0, 100, "低功率 (0-100kWh)"),
                (100, 300, "中低功率 (100-300kWh)"),
                (300, 600, "中功率 (300-600kWh)"),
                (600, 1000, "中高功率 (600-1000kWh)"),
                (1000, float('inf'), "高功率 (>1000kWh)")
            ]
            
            print(f"  不同功率范围的±10kWh准确率:")
            for low, high, label in power_ranges:
                if high == float('inf'):
                    mask = df['actual_vice_power'] > low
                else:
                    mask = (df['actual_vice_power'] > low) & (df['actual_vice_power'] <= high)
                
                if mask.sum() > 0:
                    range_df = df[mask]
                    accuracy = range_df['within_10kWh'].mean() * 100
                    print(f"    {label}: {accuracy:.1f}% ({range_df['within_10kWh'].sum()}/{len(range_df)})")

def generate_summary_statistics():
    """生成汇总统计"""
    print(f"\n{'='*80}")
    print(f"📋 汇总统计报告")
    print(f"{'='*80}")
    
    # 读取测试汇总
    if Path("test_summary.csv").exists():
        summary_df = pd.read_csv("test_summary.csv")
        
        print(f"测试方法对比:")
        print(f"{'测试方法':<15} {'样本数':<8} {'MAE':<10} {'±10kWh准确率':<12}")
        print("-" * 50)
        
        for _, row in summary_df.iterrows():
            print(f"{row['test_name']:<15} {row['sample_count']:<8} {row['mae']:<9.2f} {row['acc_10']:<11.2f}%")
        
        # 计算总体统计
        total_samples = summary_df['sample_count'].sum()
        weighted_mae = (summary_df['mae'] * summary_df['sample_count']).sum() / total_samples
        weighted_acc = (summary_df['acc_10'] * summary_df['sample_count']).sum() / total_samples
        
        print("-" * 50)
        print(f"{'加权平均':<15} {total_samples:<8} {weighted_mae:<9.2f} {weighted_acc:<11.2f}%")
        
        # 性能评级
        print(f"\n性能评级:")
        if weighted_acc >= 85:
            grade = "优秀 (A)"
        elif weighted_acc >= 80:
            grade = "良好 (B)"
        elif weighted_acc >= 75:
            grade = "中等 (C)"
        elif weighted_acc >= 70:
            grade = "及格 (D)"
        else:
            grade = "不及格 (F)"
        
        print(f"  总体±10kWh准确率: {weighted_acc:.2f}%")
        print(f"  性能等级: {grade}")
        
        # 与原始模型对比
        original_acc = 85.38  # 原始模型准确率
        improvement = weighted_acc - original_acc
        print(f"  与原始模型对比: {improvement:+.2f}%")
        
        if improvement > 0:
            print(f"  ✅ 测试结果优于原始模型")
        elif improvement > -2:
            print(f"  ⚠️ 测试结果接近原始模型")
        else:
            print(f"  ❌ 测试结果低于原始模型")

def main():
    """主函数"""
    # 分析预测误差
    all_results = analyze_prediction_errors()
    
    # 详细误差分解
    detailed_error_breakdown()
    
    # 预测质量分析
    analyze_prediction_quality()
    
    # 生成汇总统计
    generate_summary_statistics()
    
    print(f"\n{'='*80}")
    print(f"✅ 误差分析完成！")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
