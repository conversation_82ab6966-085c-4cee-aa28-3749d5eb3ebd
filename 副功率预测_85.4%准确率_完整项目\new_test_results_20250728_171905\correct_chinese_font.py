#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的中文字体设置方案
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path
import warnings
import platform
import os
warnings.filterwarnings('ignore')

def setup_chinese_font_correctly():
    """正确设置中文字体"""
    print("🔧 正确设置中文字体...")
    
    # 方法1: 直接指定系统中文字体
    system = platform.system()
    
    if system == "Windows":
        # Windows系统常见中文字体
        font_candidates = [
            'Microsoft YaHei',
            'SimHei', 
            'SimSun',
            'KaiTi',
            'FangSong'
        ]
    elif system == "Darwin":  # macOS
        font_candidates = [
            'PingFang SC',
            'Hiragino Sans GB',
            'STHeiti',
            'STSong'
        ]
    else:  # Linux
        font_candidates = [
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'Droid Sans Fallback',
            'Noto Sans CJK SC'
        ]
    
    # 获取系统所有可用字体
    available_fonts = set([f.name for f in fm.fontManager.ttflist])
    print(f"系统可用字体数量: {len(available_fonts)}")
    
    # 查找可用的中文字体
    found_font = None
    for font in font_candidates:
        if font in available_fonts:
            found_font = font
            print(f"✅ 找到中文字体: {font}")
            break
    
    if found_font:
        # 设置matplotlib参数
        plt.rcParams['font.sans-serif'] = [found_font]
        plt.rcParams['axes.unicode_minus'] = False
        
        # 验证字体设置
        test_fig, test_ax = plt.subplots(figsize=(1, 1))
        test_ax.text(0.5, 0.5, '中文测试', fontsize=12, ha='center')
        test_ax.set_title('字体测试')
        plt.close(test_fig)
        
        print(f"✅ 中文字体设置成功: {found_font}")
        return True
    else:
        print("⚠️ 未找到合适的中文字体")
        
        # 方法2: 尝试通过字体文件路径设置
        if system == "Windows":
            # Windows字体文件路径
            font_paths = [
                r"C:\Windows\Fonts\msyh.ttc",  # 微软雅黑
                r"C:\Windows\Fonts\simhei.ttf",  # 黑体
                r"C:\Windows\Fonts\simsun.ttc",  # 宋体
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        # 添加字体到matplotlib
                        fm.fontManager.addfont(font_path)
                        font_prop = fm.FontProperties(fname=font_path)
                        font_name = font_prop.get_name()
                        
                        plt.rcParams['font.sans-serif'] = [font_name]
                        plt.rcParams['axes.unicode_minus'] = False
                        
                        print(f"✅ 通过文件路径设置字体成功: {font_name}")
                        return True
                    except Exception as e:
                        print(f"字体文件加载失败: {e}")
                        continue
        
        # 方法3: 使用matplotlib内置的fallback机制
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        print("⚠️ 使用默认字体，中文可能显示为方框")
        return False

def create_test_plot():
    """创建测试图表验证中文显示"""
    print("\n📊 创建测试图表验证中文显示...")
    
    # 加载数据
    if Path('时间序列分割测试_predictions.csv').exists():
        df = pd.read_csv('时间序列分割测试_predictions.csv')
        
        # 创建简单的测试图
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], alpha=0.6, s=30)
        
        # 中文标签测试
        ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
        ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
        ax.set_title('副功率预测结果 - 中文字体测试', fontsize=14, fontweight='bold')
        
        # 添加统计信息
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.text(0.05, 0.95, f'平均绝对误差: {mae:.2f} kWh\n±10kWh准确率: {acc_10:.1f}%', 
                transform=ax.transAxes, fontsize=11, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('Chinese_Font_Test.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 测试图表已保存: Chinese_Font_Test.png")
        print("请检查图表中的中文是否正常显示")
    else:
        print("❌ 未找到测试数据文件")

def fix_original_visualization():
    """修复原始可视化脚本"""
    print("\n🔧 修复原始可视化脚本...")
    
    # 读取原始脚本
    original_script = Path('visualization_analysis.py')
    if original_script.exists():
        with open(original_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有字体设置
        if 'setup_chinese_font' in content:
            print("✅ 原始脚本已包含字体设置")
        else:
            # 在脚本开头添加正确的字体设置
            font_setup_code = '''
# 正确的中文字体设置
import matplotlib.font_manager as fm
import platform
import os

def setup_font_properly():
    """正确设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        # 尝试Windows常见中文字体
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun']
        for font in fonts:
            if font in [f.name for f in fm.fontManager.ttflist]:
                plt.rcParams['font.sans-serif'] = [font]
                plt.rcParams['axes.unicode_minus'] = False
                return font
        
        # 尝试字体文件路径
        font_files = [
            r"C:\\Windows\\Fonts\\msyh.ttc",
            r"C:\\Windows\\Fonts\\simhei.ttf"
        ]
        for font_file in font_files:
            if os.path.exists(font_file):
                try:
                    fm.fontManager.addfont(font_file)
                    prop = fm.FontProperties(fname=font_file)
                    plt.rcParams['font.sans-serif'] = [prop.get_name()]
                    plt.rcParams['axes.unicode_minus'] = False
                    return prop.get_name()
                except:
                    continue
    
    # 默认设置
    plt.rcParams['font.sans-serif'] = ['Arial']
    plt.rcParams['axes.unicode_minus'] = False
    return 'Arial'

# 设置字体
selected_font = setup_font_properly()
print(f"使用字体: {selected_font}")

'''
            
            # 在import语句后添加字体设置
            import_end = content.find('warnings.filterwarnings')
            if import_end != -1:
                insert_pos = content.find('\n', import_end) + 1
                new_content = content[:insert_pos] + font_setup_code + content[insert_pos:]
                
                # 保存修复后的脚本
                with open('visualization_analysis_fixed.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ 已创建修复后的脚本: visualization_analysis_fixed.py")
            else:
                print("❌ 无法找到合适的插入位置")
    else:
        print("❌ 未找到原始可视化脚本")

def main():
    """主函数"""
    print("🎨 正确的中文字体设置方案")
    print("="*50)
    
    # 设置中文字体
    font_success = setup_chinese_font_correctly()
    
    # 创建测试图表
    create_test_plot()
    
    # 修复原始脚本
    fix_original_visualization()
    
    print("\n🎯 字体设置完成！")
    
    if font_success:
        print("✅ 中文字体设置成功，图表应该能正常显示中文")
    else:
        print("⚠️ 中文字体设置可能有问题，建议检查系统字体")
    
    print("\n📋 建议:")
    print("1. 检查 Chinese_Font_Test.png 中的中文显示效果")
    print("2. 如果中文显示正常，可以使用修复后的脚本")
    print("3. 如果仍有问题，可能需要安装中文字体")

if __name__ == "__main__":
    main()
