#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测模型测试结果可视化分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('default')
sns.set_palette("husl")

def load_test_data():
    """加载所有测试数据"""
    print("📊 加载测试数据...")
    
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
            print(f"  ✅ {test_name}: {len(df)} 样本")
        else:
            print(f"  ❌ 文件不存在: {filename}")
    
    return data

def create_scatter_plots(data):
    """创建真实值vs预测值散点图"""
    print("\n📈 创建散点图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('副功率预测结果：真实值 vs 预测值', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的散点图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=30, label=f'{test_name}测试')
        
        # 理想预测线 y=x
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
        
        # ±10kWh误差范围
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green', label='±10kWh范围')
        
        # 计算统计指标
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
        ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
        ax.set_title(f'{test_name}测试\nR²={r2:.3f}, MAE={mae:.1f}kWh, ±10kWh准确率={acc_10:.1f}%', 
                    fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并散点图
    ax = axes[1, 1]
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=20, label=f'{test_name}测试')
    
    # 合并数据的统计
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green', label='±10kWh范围')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
    ax.set_title(f'所有测试合并\nR²={r2_all:.3f}, MAE={mae_all:.1f}kWh, ±10kWh准确率={acc_10_all:.1f}%', 
                fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('副功率预测散点图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 散点图已保存: 副功率预测散点图.png")

def create_error_distribution(data):
    """创建误差分布直方图"""
    print("\n📊 创建误差分布图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('预测误差分布分析', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的误差分布
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 直方图
        ax.hist(df['absolute_error'], bins=30, alpha=0.7, color=colors[idx], 
               edgecolor='black', linewidth=0.5)
        
        # 添加统计线
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'平均值: {mean_error:.1f}kWh')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'中位数: {median_error:.1f}kWh')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
        
        ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
        ax.set_ylabel('样本数量', fontsize=12)
        ax.set_title(f'{test_name}测试误差分布', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并误差分布对比
    ax = axes[1, 1]
    all_errors = []
    labels = []
    
    for test_name, df in data.items():
        all_errors.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors, bins=30, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
    
    ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
    ax.set_ylabel('样本数量', fontsize=12)
    ax.set_title('所有测试误差分布对比', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('预测误差分布图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 误差分布图已保存: 预测误差分布图.png")

def create_error_trend(data):
    """创建误差趋势图"""
    print("\n📈 创建误差趋势图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('预测误差随样本变化趋势', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的误差趋势
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 误差趋势线
        sample_indices = range(len(df))
        ax.plot(sample_indices, df['absolute_error'], color=colors[idx], alpha=0.7, linewidth=1)
        ax.scatter(sample_indices, df['absolute_error'], color=colors[idx], alpha=0.5, s=10)
        
        # 添加阈值线
        ax.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
        ax.axhline(df['absolute_error'].mean(), color='red', linestyle='--', linewidth=1, 
                  label=f'平均误差: {df["absolute_error"].mean():.1f}kWh')
        
        ax.set_xlabel('样本序号', fontsize=12)
        ax.set_ylabel('绝对误差 (kWh)', fontsize=12)
        ax.set_title(f'{test_name}测试误差趋势', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并趋势对比
    ax = axes[1, 1]
    offset = 0
    
    for idx, (test_name, df) in enumerate(data.items()):
        sample_indices = range(offset, offset + len(df))
        ax.plot(sample_indices, df['absolute_error'], color=colors[idx], alpha=0.7, 
               linewidth=1, label=f'{test_name}测试')
        offset += len(df)
    
    ax.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax.set_xlabel('样本序号', fontsize=12)
    ax.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax.set_title('所有测试误差趋势对比', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('预测误差趋势图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 误差趋势图已保存: 预测误差趋势图.png")

def create_boxplot_comparison(data):
    """创建误差对比箱线图"""
    print("\n📦 创建箱线图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('不同测试方法的误差对比分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    error_data = []
    test_labels = []
    
    for test_name, df in data.items():
        error_data.extend(df['absolute_error'].tolist())
        test_labels.extend([test_name] * len(df))
    
    error_df = pd.DataFrame({
        'absolute_error': error_data,
        'test_method': test_labels
    })
    
    # 箱线图
    sns.boxplot(data=error_df, x='test_method', y='absolute_error', ax=ax1)
    ax1.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax1.set_xlabel('测试方法', fontsize=12)
    ax1.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax1.set_title('误差分布箱线图', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 小提琴图
    sns.violinplot(data=error_df, x='test_method', y='absolute_error', ax=ax2)
    ax2.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax2.set_xlabel('测试方法', fontsize=12)
    ax2.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax2.set_title('误差分布小提琴图', fontsize=11)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('误差对比箱线图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 箱线图已保存: 误差对比箱线图.png")

def create_accuracy_comparison(data):
    """创建准确率对比柱状图"""
    print("\n📊 创建准确率对比图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('±10kWh准确率对比分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    test_names = list(data.keys())
    accuracies = []
    sample_counts = []
    within_10_counts = []
    
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
        sample_counts.append(len(df))
        within_10_counts.append((df['absolute_error'] <= 10).sum())
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 准确率柱状图
    bars1 = ax1.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax1.axhline(85.38, color='red', linestyle='--', linewidth=2, label='原始模型准确率 (85.38%)')
    ax1.set_ylabel('±10kWh准确率 (%)', fontsize=12)
    ax1.set_title('±10kWh准确率对比', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 样本数量对比
    bars2 = ax2.bar(test_names, within_10_counts, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('±10kWh内样本数', fontsize=12)
    ax2.set_title('±10kWh内样本数量对比', fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, count, total in zip(bars2, within_10_counts, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}/{total}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('准确率对比图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 准确率对比图已保存: 准确率对比图.png")

def create_power_range_analysis(data):
    """创建不同功率范围的预测表现分析"""
    print("\n⚡ 创建功率范围分析图...")

    # 定义功率范围
    power_ranges = [
        (0, 100, "低功率\n(0-100kWh)"),
        (100, 300, "中低功率\n(100-300kWh)"),
        (300, 600, "中功率\n(300-600kWh)"),
        (600, 1000, "中高功率\n(600-1000kWh)"),
        (1000, float('inf'), "高功率\n(>1000kWh)")
    ]

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('不同功率范围的预测表现分析', fontsize=16, fontweight='bold')

    # 准备数据
    range_data = {test_name: {range_name: [] for _, _, range_name in power_ranges} for test_name in data.keys()}
    range_accuracies = {test_name: [] for test_name in data.keys()}
    range_names = [range_name for _, _, range_name in power_ranges]

    for test_name, df in data.items():
        test_accuracies = []
        for low, high, range_name in power_ranges:
            if high == float('inf'):
                mask = df['actual_vice_power'] > low
            else:
                mask = (df['actual_vice_power'] > low) & (df['actual_vice_power'] <= high)

            if mask.sum() > 0:
                range_df = df[mask]
                accuracy = (range_df['absolute_error'] <= 10).mean() * 100
                range_data[test_name][range_name] = range_df['absolute_error'].tolist()
                test_accuracies.append(accuracy)
            else:
                test_accuracies.append(0)

        range_accuracies[test_name] = test_accuracies

    # 1. 功率范围准确率对比
    ax = axes[0, 0]
    x = np.arange(len(range_names))
    width = 0.25
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

    for i, (test_name, accuracies) in enumerate(range_accuracies.items()):
        ax.bar(x + i*width, accuracies, width, label=test_name, color=colors[i], alpha=0.8)

    ax.set_xlabel('功率范围', fontsize=12)
    ax.set_ylabel('±10kWh准确率 (%)', fontsize=12)
    ax.set_title('不同功率范围的±10kWh准确率', fontsize=11)
    ax.set_xticks(x + width)
    ax.set_xticklabels(range_names)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')

    # 2. 功率范围样本分布
    ax = axes[0, 1]
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    ax.hist(all_actual, bins=30, alpha=0.7, color='skyblue', edgecolor='black')

    # 添加功率范围分界线
    for low, high, _ in power_ranges[:-1]:
        if high != float('inf'):
            ax.axvline(high, color='red', linestyle='--', alpha=0.7)

    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('样本数量', fontsize=12)
    ax.set_title('样本在不同功率范围的分布', fontsize=11)
    ax.grid(True, alpha=0.3)

    # 3. 误差vs功率散点图
    ax = axes[1, 0]
    colors_scatter = ['#FF6B6B', '#4ECDC4', '#45B7D1']

    for i, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['absolute_error'],
                  alpha=0.6, color=colors_scatter[i], s=20, label=test_name)

    ax.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh阈值')
    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax.set_title('误差随功率变化趋势', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 4. 相对误差分析
    ax = axes[1, 1]

    for i, (test_name, df) in enumerate(data.items()):
        relative_error = (df['absolute_error'] / df['actual_vice_power']) * 100
        ax.scatter(df['actual_vice_power'], relative_error,
                  alpha=0.6, color=colors_scatter[i], s=20, label=test_name)

    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('相对误差 (%)', fontsize=12)
    ax.set_title('相对误差随功率变化趋势', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('功率范围分析图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 功率范围分析图已保存: 功率范围分析图.png")

def identify_outliers_and_difficult_samples(data):
    """识别异常值和预测困难的样本"""
    print("\n🔍 识别异常值和困难样本...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('异常值和预测困难样本分析', fontsize=16, fontweight='bold')

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

    # 1. 最大误差样本分析
    ax = axes[0, 0]
    max_errors = []
    test_names = []

    for test_name, df in data.items():
        # 找出误差最大的前10个样本
        top_errors = df.nlargest(10, 'absolute_error')
        max_errors.extend(top_errors['absolute_error'].tolist())
        test_names.extend([test_name] * len(top_errors))

    error_df = pd.DataFrame({'error': max_errors, 'test': test_names})
    sns.barplot(data=error_df, x='test', y='error', ax=ax)
    ax.set_ylabel('绝对误差 (kWh)', fontsize=12)
    ax.set_title('各测试方法的最大误差样本', fontsize=11)
    ax.grid(True, alpha=0.3, axis='y')

    # 2. 误差超过阈值的样本比例
    ax = axes[0, 1]
    thresholds = [5, 10, 15, 20, 30, 50]

    for i, (test_name, df) in enumerate(data.items()):
        proportions = []
        for threshold in thresholds:
            prop = (df['absolute_error'] > threshold).mean() * 100
            proportions.append(prop)

        ax.plot(thresholds, proportions, marker='o', linewidth=2,
               label=test_name, color=colors[i])

    ax.set_xlabel('误差阈值 (kWh)', fontsize=12)
    ax.set_ylabel('超过阈值的样本比例 (%)', fontsize=12)
    ax.set_title('不同误差阈值下的样本比例', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 3. 预测偏差分析
    ax = axes[1, 0]

    for i, (test_name, df) in enumerate(data.items()):
        bias = df['predicted_vice_power'] - df['actual_vice_power']
        ax.hist(bias, bins=30, alpha=0.6, label=test_name, color=colors[i])

    ax.axvline(0, color='black', linestyle='--', linewidth=2, label='无偏差线')
    ax.set_xlabel('预测偏差 (预测值 - 真实值) kWh', fontsize=12)
    ax.set_ylabel('样本数量', fontsize=12)
    ax.set_title('预测偏差分布', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 4. 困难样本特征分析
    ax = axes[1, 1]

    # 定义困难样本（误差>20kWh）
    difficult_samples = []
    easy_samples = []

    for test_name, df in data.items():
        difficult = df[df['absolute_error'] > 20]
        easy = df[df['absolute_error'] <= 10]

        if len(difficult) > 0:
            difficult_samples.extend(difficult['actual_vice_power'].tolist())
        if len(easy) > 0:
            easy_samples.extend(easy['actual_vice_power'].tolist())

    if difficult_samples and easy_samples:
        ax.hist([easy_samples, difficult_samples], bins=20, alpha=0.7,
               label=['易预测样本 (误差≤10kWh)', '困难样本 (误差>20kWh)'],
               color=['green', 'red'])

        ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
        ax.set_ylabel('样本数量', fontsize=12)
        ax.set_title('易预测vs困难样本的功率分布', fontsize=11)
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('异常值分析图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 异常值分析图已保存: 异常值分析图.png")

def generate_statistics_summary(data):
    """生成详细统计摘要"""
    print("\n📋 生成统计摘要...")

    summary_stats = {}

    for test_name, df in data.items():
        stats = {
            'sample_count': len(df),
            'mae': df['absolute_error'].mean(),
            'rmse': np.sqrt((df['absolute_error'] ** 2).mean()),
            'median_error': df['absolute_error'].median(),
            'std_error': df['absolute_error'].std(),
            'max_error': df['absolute_error'].max(),
            'min_error': df['absolute_error'].min(),
            'q75_error': df['absolute_error'].quantile(0.75),
            'q90_error': df['absolute_error'].quantile(0.90),
            'q95_error': df['absolute_error'].quantile(0.95),
            'acc_5': (df['absolute_error'] <= 5).mean() * 100,
            'acc_10': (df['absolute_error'] <= 10).mean() * 100,
            'acc_15': (df['absolute_error'] <= 15).mean() * 100,
            'acc_20': (df['absolute_error'] <= 20).mean() * 100,
            'correlation': df['actual_vice_power'].corr(df['predicted_vice_power']),
            'r_squared': df['actual_vice_power'].corr(df['predicted_vice_power']) ** 2,
            'mean_bias': (df['predicted_vice_power'] - df['actual_vice_power']).mean(),
            'outliers_count': (df['absolute_error'] > 50).sum(),
            'outliers_percentage': (df['absolute_error'] > 50).mean() * 100
        }
        summary_stats[test_name] = stats

    # 保存统计摘要
    stats_df = pd.DataFrame(summary_stats).T
    stats_df.to_csv('详细统计摘要.csv', encoding='utf-8-sig')

    print("  ✅ 详细统计摘要已保存: 详细统计摘要.csv")

    return summary_stats

def main():
    """主函数"""
    print("🎨 副功率预测模型可视化分析")
    print("="*50)

    # 加载数据
    data = load_test_data()

    if not data:
        print("❌ 没有找到测试数据文件！")
        return

    # 创建各种可视化图表
    create_scatter_plots(data)
    create_error_distribution(data)
    create_error_trend(data)
    create_boxplot_comparison(data)
    create_accuracy_comparison(data)
    create_power_range_analysis(data)
    identify_outliers_and_difficult_samples(data)

    # 生成统计摘要
    summary_stats = generate_statistics_summary(data)

    print("\n🎯 可视化分析完成！")
    print("所有图表已保存为PNG文件（300 DPI）")
    print("详细统计数据已保存为CSV文件")

if __name__ == "__main__":
    main()
