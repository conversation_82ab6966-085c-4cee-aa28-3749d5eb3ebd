#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的可视化分析 - 正确的中文字体显示
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path
import warnings
import platform
import os
warnings.filterwarnings('ignore')

# 正确设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        # Windows系统字体
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'Droid Sans Fallback', 'Noto Sans CJK SC']
    
    # 查找可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    for font in fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✅ 使用字体: {font}")
            return font
    
    # 如果没找到，尝试字体文件路径（Windows）
    if system == "Windows":
        font_files = [
            (r"C:\Windows\Fonts\msyh.ttc", "Microsoft YaHei"),
            (r"C:\Windows\Fonts\simhei.ttf", "SimHei"),
        ]
        
        for font_path, font_name in font_files:
            if os.path.exists(font_path):
                try:
                    fm.fontManager.addfont(font_path)
                    prop = fm.FontProperties(fname=font_path)
                    plt.rcParams['font.sans-serif'] = [prop.get_name()]
                    plt.rcParams['axes.unicode_minus'] = False
                    print(f"✅ 通过文件路径使用字体: {font_name}")
                    return font_name
                except:
                    continue
    
    # 默认字体
    plt.rcParams['font.sans-serif'] = ['Arial']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️ 使用默认字体，中文可能显示异常")
    return 'Arial'

# 设置字体
selected_font = setup_chinese_font()

# 设置图表样式
plt.style.use('default')
sns.set_palette("husl")

def load_test_data():
    """加载测试数据"""
    print("📊 加载测试数据...")
    
    test_files = {
        '时间序列分割': '时间序列分割测试_predictions.csv',
        '随机分割': '随机分割测试_predictions.csv',
        '设备分割': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
            print(f"  ✅ {test_name}: {len(df)} 样本")
        else:
            print(f"  ❌ 文件不存在: {filename}")
    
    return data

def create_final_scatter_plot(data):
    """创建最终版散点图"""
    print("\n📈 创建最终版散点图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('副功率预测结果：真实值 vs 预测值', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 为每个测试创建散点图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=30, label=f'{test_name}测试')
        
        # 理想预测线 y=x
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
        
        # ±10kWh误差范围
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green', label='±10kWh范围')
        
        # 计算统计指标
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
        ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
        ax.set_title(f'{test_name}测试\nR²={r2:.3f}, MAE={mae:.1f}kWh, ±10kWh准确率={acc_10:.1f}%', 
                    fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并散点图
    ax = axes[1, 1]
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=20, label=f'{test_name}测试')
    
    # 合并数据的统计
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='理想预测线')
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green', label='±10kWh范围')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('真实副功率 (kWh)', fontsize=12)
    ax.set_ylabel('预测副功率 (kWh)', fontsize=12)
    ax.set_title(f'所有测试合并\nR²={r2_all:.3f}, MAE={mae_all:.1f}kWh, ±10kWh准确率={acc_10_all:.1f}%', 
                fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('最终版_副功率预测散点图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 最终版散点图已保存: 最终版_副功率预测散点图.png")

def create_final_accuracy_plot(data):
    """创建最终版准确率对比图"""
    print("\n📊 创建最终版准确率对比图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('±10kWh准确率对比分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    test_names = list(data.keys())
    accuracies = []
    sample_counts = []
    within_10_counts = []
    
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
        sample_counts.append(len(df))
        within_10_counts.append((df['absolute_error'] <= 10).sum())
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 准确率柱状图
    bars1 = ax1.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax1.axhline(85.38, color='red', linestyle='--', linewidth=2, label='原始模型准确率 (85.38%)')
    ax1.set_ylabel('±10kWh准确率 (%)', fontsize=12)
    ax1.set_title('±10kWh准确率对比', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 样本数量对比
    bars2 = ax2.bar(test_names, within_10_counts, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('±10kWh内样本数', fontsize=12)
    ax2.set_title('±10kWh内样本数量对比', fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, count, total in zip(bars2, within_10_counts, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}/{total}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('最终版_准确率对比图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 最终版准确率对比图已保存: 最终版_准确率对比图.png")

def create_final_error_distribution(data):
    """创建最终版误差分布图"""
    print("\n📊 创建最终版误差分布图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('预测误差分布分析', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的误差分布
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 直方图
        ax.hist(df['absolute_error'], bins=30, alpha=0.7, color=colors[idx], 
               edgecolor='black', linewidth=0.5)
        
        # 添加统计线
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'平均值: {mean_error:.1f}kWh')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'中位数: {median_error:.1f}kWh')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
        
        ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
        ax.set_ylabel('样本数量', fontsize=12)
        ax.set_title(f'{test_name}测试误差分布', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并误差分布对比
    ax = axes[1, 1]
    all_errors = []
    labels = []
    
    for test_name, df in data.items():
        all_errors.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors, bins=30, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh阈值')
    
    ax.set_xlabel('绝对误差 (kWh)', fontsize=12)
    ax.set_ylabel('样本数量', fontsize=12)
    ax.set_title('所有测试误差分布对比', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('最终版_预测误差分布图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ 最终版误差分布图已保存: 最终版_预测误差分布图.png")

def main():
    """主函数"""
    print("🎨 最终版可视化分析 - 正确的中文字体显示")
    print("="*60)
    print(f"当前使用字体: {selected_font}")
    
    # 加载数据
    data = load_test_data()
    
    if not data:
        print("❌ 没有找到测试数据文件！")
        return
    
    # 创建最终版图表
    create_final_scatter_plot(data)
    create_final_accuracy_plot(data)
    create_final_error_distribution(data)
    
    print("\n🎯 最终版可视化分析完成！")
    print("生成的图表文件:")
    print("- 最终版_副功率预测散点图.png")
    print("- 最终版_准确率对比图.png")
    print("- 最终版_预测误差分布图.png")
    print("\n✅ 所有图表均使用正确的中文字体，应该能正常显示中文")

if __name__ == "__main__":
    main()
