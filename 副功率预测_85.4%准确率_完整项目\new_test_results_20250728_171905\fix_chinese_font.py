#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复中文字体显示问题的可视化脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
import platform
warnings.filterwarnings('ignore')

def setup_font():
    """设置字体"""
    import matplotlib.font_manager as fm
    
    # 尝试不同的字体设置方法
    system = platform.system()
    
    # 方法1: 直接设置常用字体
    if system == "Windows":
        try:
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
            print("✅ Windows字体设置完成")
            return True
        except:
            pass
    
    # 方法2: 使用系统默认字体
    try:
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("⚠️ 使用英文字体，中文可能显示为方框")
        return False
    except:
        print("❌ 字体设置失败")
        return False

def create_fixed_scatter_plot():
    """创建修复字体的散点图"""
    print("📈 创建修复字体的散点图...")
    
    # 加载数据
    test_files = {
        'Time Series': '时间序列分割测试_predictions.csv',
        'Random Split': '随机分割测试_predictions.csv',
        'Device Split': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        print("❌ 没有找到数据文件")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Vice Power Prediction: Actual vs Predicted', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 为每个测试创建散点图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=30)
        
        # 理想预测线
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='Perfect Line')
        
        # ±10kWh范围
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green', label='±10kWh Range')
        
        # 统计指标
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
        ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=12)
        ax.set_title(f'{test_name}\nR²={r2:.3f}, MAE={mae:.1f}kWh, Acc={acc_10:.1f}%', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并图
    ax = axes[1, 1]
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=20, label=test_name)
    
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='Perfect Line')
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green', label='±10kWh Range')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
    ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=12)
    ax.set_title(f'All Tests Combined\nR²={r2_all:.3f}, MAE={mae_all:.1f}kWh, Acc={acc_10_all:.1f}%', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Fixed_Scatter_Plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 修复字体的散点图已保存: Fixed_Scatter_Plot.png")

def create_fixed_accuracy_plot():
    """创建修复字体的准确率对比图"""
    print("📊 创建修复字体的准确率对比图...")
    
    # 加载数据
    test_files = {
        'Time Series': '时间序列分割测试_predictions.csv',
        'Random Split': '随机分割测试_predictions.csv',
        'Device Split': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        return
    
    # 准备数据
    test_names = list(data.keys())
    accuracies = []
    within_10_counts = []
    sample_counts = []
    
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
        within_10_counts.append((df['absolute_error'] <= 10).sum())
        sample_counts.append(len(df))
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('±10kWh Accuracy Comparison', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 准确率柱状图
    bars1 = ax1.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax1.axhline(85.38, color='red', linestyle='--', linewidth=2, label='Original Model (85.38%)')
    ax1.set_ylabel('±10kWh Accuracy (%)', fontsize=12)
    ax1.set_title('±10kWh Accuracy Comparison', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 样本数量图
    bars2 = ax2.bar(test_names, within_10_counts, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('Samples within ±10kWh', fontsize=12)
    ax2.set_title('Sample Count within ±10kWh', fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, count, total in zip(bars2, within_10_counts, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}/{total}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('Fixed_Accuracy_Plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 修复字体的准确率图已保存: Fixed_Accuracy_Plot.png")

def create_fixed_error_distribution():
    """创建修复字体的误差分布图"""
    print("📊 创建修复字体的误差分布图...")
    
    # 加载数据
    test_files = {
        'Time Series': '时间序列分割测试_predictions.csv',
        'Random Split': '随机分割测试_predictions.csv',
        'Device Split': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
    
    if not data:
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Prediction Error Distribution Analysis', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 为每个测试创建误差分布图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 直方图
        ax.hist(df['absolute_error'], bins=30, alpha=0.7, color=colors[idx], edgecolor='black', linewidth=0.5)
        
        # 统计线
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_error:.1f}kWh')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'Median: {median_error:.1f}kWh')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh Threshold')
        
        ax.set_xlabel('Absolute Error (kWh)', fontsize=12)
        ax.set_ylabel('Sample Count', fontsize=12)
        ax.set_title(f'{test_name} Error Distribution', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并误差分布
    ax = axes[1, 1]
    all_errors = []
    labels = []
    
    for test_name, df in data.items():
        all_errors.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors, bins=30, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh Threshold')
    
    ax.set_xlabel('Absolute Error (kWh)', fontsize=12)
    ax.set_ylabel('Sample Count', fontsize=12)
    ax.set_title('All Tests Error Distribution', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Fixed_Error_Distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 修复字体的误差分布图已保存: Fixed_Error_Distribution.png")

def main():
    """主函数"""
    print("🔧 修复中文字体显示问题")
    print("="*50)
    
    # 设置字体
    font_ok = setup_font()
    
    if font_ok:
        print("✅ 中文字体设置成功")
    else:
        print("⚠️ 使用英文字体，避免显示问题")
    
    # 创建修复后的图表
    create_fixed_scatter_plot()
    create_fixed_accuracy_plot()
    create_fixed_error_distribution()
    
    print("\n🎯 字体修复完成！")
    print("生成的图表文件:")
    print("- Fixed_Scatter_Plot.png")
    print("- Fixed_Accuracy_Plot.png") 
    print("- Fixed_Error_Distribution.png")

if __name__ == "__main__":
    main()
