#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可靠的可视化脚本 - 使用英文标签确保显示正常
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 使用默认字体，确保显示正常
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']

def load_test_data():
    """Load test data"""
    print("📊 Loading test data...")
    
    test_files = {
        'Time Series Split': '时间序列分割测试_predictions.csv',
        'Random Split': '随机分割测试_predictions.csv',
        'Device Split': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
            print(f"  ✅ {test_name}: {len(df)} samples")
        else:
            print(f"  ❌ File not found: {filename}")
    
    return data

def create_comprehensive_analysis(data):
    """Create comprehensive analysis plots"""
    print("\n📈 Creating comprehensive analysis plots...")
    
    # Create a large figure with multiple subplots
    fig = plt.figure(figsize=(20, 16))
    
    # Define colors for different test methods
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    test_names = list(data.keys())
    
    # 1. Scatter plots (2x2 grid in top half)
    for idx, (test_name, df) in enumerate(data.items()):
        ax = plt.subplot(4, 3, idx + 1)
        
        # Scatter plot
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=20)
        
        # Perfect prediction line
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2)
        
        # ±10kWh range
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green')
        
        # Statistics
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('Actual Vice Power (kWh)', fontsize=10)
        ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=10)
        ax.set_title(f'{test_name}\nR²={r2:.3f}, MAE={mae:.1f}kWh, Acc={acc_10:.1f}%', fontsize=10)
        ax.grid(True, alpha=0.3)
    
    # 2. Combined scatter plot
    ax = plt.subplot(4, 3, 4)
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=15, label=test_name)
    
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2)
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=10)
    ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=10)
    ax.set_title(f'All Tests Combined\nR²={r2_all:.3f}, MAE={mae_all:.1f}kWh, Acc={acc_10_all:.1f}%', fontsize=10)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    
    # 3. Error distribution plots
    for idx, (test_name, df) in enumerate(data.items()):
        ax = plt.subplot(4, 3, idx + 5)
        
        ax.hist(df['absolute_error'], bins=25, alpha=0.7, color=colors[idx], edgecolor='black', linewidth=0.5)
        
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_error:.1f}')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'Median: {median_error:.1f}')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh')
        
        ax.set_xlabel('Absolute Error (kWh)', fontsize=10)
        ax.set_ylabel('Count', fontsize=10)
        ax.set_title(f'{test_name} Error Distribution', fontsize=10)
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
    
    # 4. Combined error distribution
    ax = plt.subplot(4, 3, 8)
    all_errors_list = []
    labels = []
    
    for test_name, df in data.items():
        all_errors_list.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors_list, bins=25, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh Threshold')
    
    ax.set_xlabel('Absolute Error (kWh)', fontsize=10)
    ax.set_ylabel('Count', fontsize=10)
    ax.set_title('Error Distribution Comparison', fontsize=10)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    
    # 5. Accuracy comparison
    ax = plt.subplot(4, 3, 9)
    accuracies = []
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
    
    bars = ax.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax.axhline(85.38, color='red', linestyle='--', linewidth=2, label='Original Model (85.38%)')
    ax.set_ylabel('±10kWh Accuracy (%)', fontsize=10)
    ax.set_title('±10kWh Accuracy Comparison', fontsize=10)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # 6. Box plot comparison
    ax = plt.subplot(4, 3, 10)
    error_data = []
    test_labels = []
    
    for test_name, df in data.items():
        error_data.extend(df['absolute_error'].tolist())
        test_labels.extend([test_name] * len(df))
    
    error_df = pd.DataFrame({'error': error_data, 'test': test_labels})
    sns.boxplot(data=error_df, x='test', y='error', ax=ax)
    ax.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh')
    ax.set_xlabel('Test Method', fontsize=10)
    ax.set_ylabel('Absolute Error (kWh)', fontsize=10)
    ax.set_title('Error Distribution Boxplot', fontsize=10)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    
    # 7. Power range analysis
    ax = plt.subplot(4, 3, 11)
    power_ranges = [(0, 100), (100, 300), (300, 600), (600, 1000), (1000, float('inf'))]
    range_labels = ['0-100', '100-300', '300-600', '600-1000', '>1000']
    
    x = np.arange(len(range_labels))
    width = 0.25
    
    for i, (test_name, df) in enumerate(data.items()):
        accuracies_by_range = []
        for low, high in power_ranges:
            if high == float('inf'):
                mask = df['actual_vice_power'] > low
            else:
                mask = (df['actual_vice_power'] > low) & (df['actual_vice_power'] <= high)
            
            if mask.sum() > 0:
                acc = (df[mask]['absolute_error'] <= 10).mean() * 100
                accuracies_by_range.append(acc)
            else:
                accuracies_by_range.append(0)
        
        ax.bar(x + i*width, accuracies_by_range, width, label=test_name, color=colors[i], alpha=0.8)
    
    ax.set_xlabel('Power Range (kWh)', fontsize=10)
    ax.set_ylabel('±10kWh Accuracy (%)', fontsize=10)
    ax.set_title('Accuracy by Power Range', fontsize=10)
    ax.set_xticks(x + width)
    ax.set_xticklabels(range_labels)
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3, axis='y')
    
    # 8. Sample distribution
    ax = plt.subplot(4, 3, 12)
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    ax.hist(all_actual, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    
    # Add power range boundaries
    boundaries = [100, 300, 600, 1000]
    for boundary in boundaries:
        ax.axvline(boundary, color='red', linestyle='--', alpha=0.7)
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=10)
    ax.set_ylabel('Sample Count', fontsize=10)
    ax.set_title('Sample Distribution by Power', fontsize=10)
    ax.grid(True, alpha=0.3)
    
    plt.suptitle('Vice Power Prediction Model - Comprehensive Analysis', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('Comprehensive_Analysis_Report.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("  ✅ Comprehensive analysis saved: Comprehensive_Analysis_Report.png")

def create_summary_table(data):
    """Create summary statistics table"""
    print("\n📊 Creating summary statistics...")
    
    summary_data = []
    for test_name, df in data.items():
        stats = {
            'Test Method': test_name,
            'Sample Count': len(df),
            'MAE (kWh)': df['absolute_error'].mean(),
            'RMSE (kWh)': np.sqrt((df['absolute_error'] ** 2).mean()),
            'Median Error (kWh)': df['absolute_error'].median(),
            'Max Error (kWh)': df['absolute_error'].max(),
            '±5kWh Accuracy (%)': (df['absolute_error'] <= 5).mean() * 100,
            '±10kWh Accuracy (%)': (df['absolute_error'] <= 10).mean() * 100,
            '±15kWh Accuracy (%)': (df['absolute_error'] <= 15).mean() * 100,
            '±20kWh Accuracy (%)': (df['absolute_error'] <= 20).mean() * 100,
            'R²': df['actual_vice_power'].corr(df['predicted_vice_power']) ** 2,
            'Within ±10kWh Count': (df['absolute_error'] <= 10).sum()
        }
        summary_data.append(stats)
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('Final_Summary_Statistics.csv', index=False)
    
    print("  ✅ Summary statistics saved: Final_Summary_Statistics.csv")
    
    # Print key results
    print("\n📋 Key Results Summary:")
    print("="*80)
    for _, row in summary_df.iterrows():
        print(f"{row['Test Method']}:")
        print(f"  Sample Count: {row['Sample Count']}")
        print(f"  ±10kWh Accuracy: {row['±10kWh Accuracy (%)']:.2f}%")
        print(f"  MAE: {row['MAE (kWh)']:.2f} kWh")
        print(f"  Within ±10kWh: {row['Within ±10kWh Count']}/{row['Sample Count']}")
        print()
    
    # Overall statistics
    total_samples = summary_df['Sample Count'].sum()
    total_within_10 = summary_df['Within ±10kWh Count'].sum()
    overall_accuracy = (total_within_10 / total_samples) * 100
    weighted_mae = (summary_df['MAE (kWh)'] * summary_df['Sample Count']).sum() / total_samples
    
    print(f"Overall Results:")
    print(f"  Total Samples: {total_samples}")
    print(f"  Overall ±10kWh Accuracy: {overall_accuracy:.2f}%")
    print(f"  Weighted Average MAE: {weighted_mae:.2f} kWh")
    print(f"  Samples within ±10kWh: {total_within_10}/{total_samples}")

def main():
    """Main function"""
    print("🎨 Reliable Visualization Analysis - English Labels")
    print("="*60)
    
    # Load data
    data = load_test_data()
    
    if not data:
        print("❌ No test data files found!")
        return
    
    # Create comprehensive analysis
    create_comprehensive_analysis(data)
    
    # Create summary statistics
    create_summary_table(data)
    
    print("\n🎯 Analysis completed successfully!")
    print("Generated files:")
    print("- Comprehensive_Analysis_Report.png (All charts in one file)")
    print("- Final_Summary_Statistics.csv (Detailed statistics)")
    print("\n✅ All charts use English labels - guaranteed to display correctly")

if __name__ == "__main__":
    main()
