#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测模型测试结果可视化分析 - 英文版本（避免字体问题）
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.style.use('default')
sns.set_palette("husl")

def load_test_data():
    """加载所有测试数据"""
    print("📊 Loading test data...")
    
    test_files = {
        'Time Series Split': '时间序列分割测试_predictions.csv',
        'Random Split': '随机分割测试_predictions.csv',
        'Device Split': '设备分割测试_predictions.csv'
    }
    
    data = {}
    for test_name, filename in test_files.items():
        if Path(filename).exists():
            df = pd.read_csv(filename)
            data[test_name] = df
            print(f"  ✅ {test_name}: {len(df)} samples")
        else:
            print(f"  ❌ File not found: {filename}")
    
    return data

def create_scatter_plots(data):
    """创建真实值vs预测值散点图"""
    print("\n📈 Creating scatter plots...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Vice Power Prediction Results: Actual vs Predicted', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的散点图
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.6, color=colors[idx], s=30, label=f'{test_name}')
        
        # 理想预测线 y=x
        min_val = min(df['actual_vice_power'].min(), df['predicted_vice_power'].min())
        max_val = max(df['actual_vice_power'].max(), df['predicted_vice_power'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='Perfect Prediction')
        
        # ±10kWh误差范围
        ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                       alpha=0.2, color='green', label='±10kWh Range')
        
        # 计算统计指标
        r2 = df['actual_vice_power'].corr(df['predicted_vice_power'])**2
        mae = df['absolute_error'].mean()
        acc_10 = (df['absolute_error'] <= 10).mean() * 100
        
        ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
        ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=12)
        ax.set_title(f'{test_name}\nR²={r2:.3f}, MAE={mae:.1f}kWh, ±10kWh Acc={acc_10:.1f}%', 
                    fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并散点图
    ax = axes[1, 1]
    for idx, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['predicted_vice_power'], 
                  alpha=0.5, color=colors[idx], s=20, label=f'{test_name}')
    
    # 合并数据的统计
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    all_predicted = pd.concat([df['predicted_vice_power'] for df in data.values()])
    all_errors = pd.concat([df['absolute_error'] for df in data.values()])
    
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='Perfect Prediction')
    ax.fill_between([min_val, max_val], [min_val-10, max_val-10], [min_val+10, max_val+10], 
                   alpha=0.2, color='green', label='±10kWh Range')
    
    r2_all = all_actual.corr(all_predicted)**2
    mae_all = all_errors.mean()
    acc_10_all = (all_errors <= 10).mean() * 100
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
    ax.set_ylabel('Predicted Vice Power (kWh)', fontsize=12)
    ax.set_title(f'All Tests Combined\nR²={r2_all:.3f}, MAE={mae_all:.1f}kWh, ±10kWh Acc={acc_10_all:.1f}%', 
                fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Vice_Power_Prediction_Scatter_EN.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ Scatter plot saved: Vice_Power_Prediction_Scatter_EN.png")

def create_error_distribution(data):
    """创建误差分布直方图"""
    print("\n📊 Creating error distribution plots...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Prediction Error Distribution Analysis', fontsize=16, fontweight='bold')
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 单独的误差分布
    for idx, (test_name, df) in enumerate(data.items()):
        row, col = idx // 2, idx % 2
        ax = axes[row, col]
        
        # 直方图
        ax.hist(df['absolute_error'], bins=30, alpha=0.7, color=colors[idx], 
               edgecolor='black', linewidth=0.5)
        
        # 添加统计线
        mean_error = df['absolute_error'].mean()
        median_error = df['absolute_error'].median()
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_error:.1f}kWh')
        ax.axvline(median_error, color='orange', linestyle='--', linewidth=2, label=f'Median: {median_error:.1f}kWh')
        ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh Threshold')
        
        ax.set_xlabel('Absolute Error (kWh)', fontsize=12)
        ax.set_ylabel('Sample Count', fontsize=12)
        ax.set_title(f'{test_name} Error Distribution', fontsize=11)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    # 合并误差分布对比
    ax = axes[1, 1]
    all_errors = []
    labels = []
    
    for test_name, df in data.items():
        all_errors.append(df['absolute_error'])
        labels.append(test_name)
    
    ax.hist(all_errors, bins=30, alpha=0.6, label=labels, color=colors[:len(data)])
    ax.axvline(10, color='green', linestyle='-', linewidth=2, label='±10kWh Threshold')
    
    ax.set_xlabel('Absolute Error (kWh)', fontsize=12)
    ax.set_ylabel('Sample Count', fontsize=12)
    ax.set_title('All Tests Error Distribution Comparison', fontsize=11)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Prediction_Error_Distribution_EN.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ Error distribution plot saved: Prediction_Error_Distribution_EN.png")

def create_accuracy_comparison(data):
    """创建准确率对比柱状图"""
    print("\n📊 Creating accuracy comparison plots...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('±10kWh Accuracy Comparison Analysis', fontsize=16, fontweight='bold')
    
    # 准备数据
    test_names = list(data.keys())
    accuracies = []
    sample_counts = []
    within_10_counts = []
    
    for test_name, df in data.items():
        acc = (df['absolute_error'] <= 10).mean() * 100
        accuracies.append(acc)
        sample_counts.append(len(df))
        within_10_counts.append((df['absolute_error'] <= 10).sum())
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 准确率柱状图
    bars1 = ax1.bar(test_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
    ax1.axhline(85.38, color='red', linestyle='--', linewidth=2, label='Original Model Accuracy (85.38%)')
    ax1.set_ylabel('±10kWh Accuracy (%)', fontsize=12)
    ax1.set_title('±10kWh Accuracy Comparison', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 样本数量对比
    bars2 = ax2.bar(test_names, within_10_counts, color=colors, alpha=0.8, edgecolor='black')
    ax2.set_ylabel('Samples within ±10kWh', fontsize=12)
    ax2.set_title('Sample Count within ±10kWh', fontsize=11)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, count, total in zip(bars2, within_10_counts, sample_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}/{total}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('Accuracy_Comparison_EN.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ Accuracy comparison plot saved: Accuracy_Comparison_EN.png")

def create_boxplot_comparison(data):
    """创建误差对比箱线图"""
    print("\n📦 Creating boxplot comparison...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('Error Distribution Comparison Analysis', fontsize=16, fontweight='bold')
    
    # 准备数据
    error_data = []
    test_labels = []
    
    for test_name, df in data.items():
        error_data.extend(df['absolute_error'].tolist())
        test_labels.extend([test_name] * len(df))
    
    error_df = pd.DataFrame({
        'absolute_error': error_data,
        'test_method': test_labels
    })
    
    # 箱线图
    sns.boxplot(data=error_df, x='test_method', y='absolute_error', ax=ax1)
    ax1.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh Threshold')
    ax1.set_xlabel('Test Method', fontsize=12)
    ax1.set_ylabel('Absolute Error (kWh)', fontsize=12)
    ax1.set_title('Error Distribution Boxplot', fontsize=11)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 小提琴图
    sns.violinplot(data=error_df, x='test_method', y='absolute_error', ax=ax2)
    ax2.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh Threshold')
    ax2.set_xlabel('Test Method', fontsize=12)
    ax2.set_ylabel('Absolute Error (kWh)', fontsize=12)
    ax2.set_title('Error Distribution Violin Plot', fontsize=11)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Error_Comparison_Boxplot_EN.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ Boxplot saved: Error_Comparison_Boxplot_EN.png")

def create_power_range_analysis(data):
    """创建不同功率范围的预测表现分析"""
    print("\n⚡ Creating power range analysis...")
    
    # 定义功率范围
    power_ranges = [
        (0, 100, "Low Power\n(0-100kWh)"),
        (100, 300, "Med-Low Power\n(100-300kWh)"),
        (300, 600, "Medium Power\n(300-600kWh)"),
        (600, 1000, "Med-High Power\n(600-1000kWh)"),
        (1000, float('inf'), "High Power\n(>1000kWh)")
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Prediction Performance Analysis by Power Range', fontsize=16, fontweight='bold')
    
    # 准备数据
    range_accuracies = {test_name: [] for test_name in data.keys()}
    range_names = [range_name for _, _, range_name in power_ranges]
    
    for test_name, df in data.items():
        test_accuracies = []
        for low, high, range_name in power_ranges:
            if high == float('inf'):
                mask = df['actual_vice_power'] > low
            else:
                mask = (df['actual_vice_power'] > low) & (df['actual_vice_power'] <= high)
            
            if mask.sum() > 0:
                range_df = df[mask]
                accuracy = (range_df['absolute_error'] <= 10).mean() * 100
                test_accuracies.append(accuracy)
            else:
                test_accuracies.append(0)
        
        range_accuracies[test_name] = test_accuracies
    
    # 1. 功率范围准确率对比
    ax = axes[0, 0]
    x = np.arange(len(range_names))
    width = 0.25
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for i, (test_name, accuracies) in enumerate(range_accuracies.items()):
        ax.bar(x + i*width, accuracies, width, label=test_name, color=colors[i], alpha=0.8)
    
    ax.set_xlabel('Power Range', fontsize=12)
    ax.set_ylabel('±10kWh Accuracy (%)', fontsize=12)
    ax.set_title('±10kWh Accuracy by Power Range', fontsize=11)
    ax.set_xticks(x + width)
    ax.set_xticklabels(range_names)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    
    # 2. 功率范围样本分布
    ax = axes[0, 1]
    all_actual = pd.concat([df['actual_vice_power'] for df in data.values()])
    ax.hist(all_actual, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    
    # 添加功率范围分界线
    for low, high, _ in power_ranges[:-1]:
        if high != float('inf'):
            ax.axvline(high, color='red', linestyle='--', alpha=0.7)
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
    ax.set_ylabel('Sample Count', fontsize=12)
    ax.set_title('Sample Distribution by Power Range', fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # 3. 误差vs功率散点图
    ax = axes[1, 0]
    colors_scatter = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for i, (test_name, df) in enumerate(data.items()):
        ax.scatter(df['actual_vice_power'], df['absolute_error'], 
                  alpha=0.6, color=colors_scatter[i], s=20, label=test_name)
    
    ax.axhline(10, color='green', linestyle='--', linewidth=2, label='±10kWh Threshold')
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
    ax.set_ylabel('Absolute Error (kWh)', fontsize=12)
    ax.set_title('Error vs Power Trend', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 4. 相对误差分析
    ax = axes[1, 1]
    
    for i, (test_name, df) in enumerate(data.items()):
        relative_error = (df['absolute_error'] / df['actual_vice_power']) * 100
        ax.scatter(df['actual_vice_power'], relative_error, 
                  alpha=0.6, color=colors_scatter[i], s=20, label=test_name)
    
    ax.set_xlabel('Actual Vice Power (kWh)', fontsize=12)
    ax.set_ylabel('Relative Error (%)', fontsize=12)
    ax.set_title('Relative Error vs Power Trend', fontsize=11)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Power_Range_Analysis_EN.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("  ✅ Power range analysis saved: Power_Range_Analysis_EN.png")

def main():
    """主函数"""
    print("🎨 Vice Power Prediction Model Visualization Analysis (English Version)")
    print("="*70)
    
    # 加载数据
    data = load_test_data()
    
    if not data:
        print("❌ No test data files found!")
        return
    
    # 创建各种可视化图表
    create_scatter_plots(data)
    create_error_distribution(data)
    create_accuracy_comparison(data)
    create_boxplot_comparison(data)
    create_power_range_analysis(data)
    
    print("\n🎯 Visualization analysis completed!")
    print("All charts saved as PNG files (300 DPI) with English labels")

if __name__ == "__main__":
    main()
